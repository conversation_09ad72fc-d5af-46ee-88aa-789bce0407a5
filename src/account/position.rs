use crate::account::types::{PositionSide, TradeRecord};
use crate::types::{OrderSide, Price};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

/// 仓位信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Position {
    /// 交易对
    pub symbol: String,
    /// 仓位方向
    pub side: PositionSide,
    /// 持仓数量（正数表示多头，负数表示空头）
    pub quantity: f64,
    /// 平均开仓价格
    pub avg_price: Price,
    /// 未实现盈亏
    pub unrealized_pnl: f64,
    /// 已实现盈亏
    pub realized_pnl: f64,
    /// 保证金
    pub margin: f64,
    /// 杠杆倍数
    pub leverage: f64,
    /// 开仓时间
    pub open_time: DateTime<Utc>,
    /// 最后更新时间
    pub last_updated: DateTime<Utc>,
}

impl Position {
    /// 创建新的空仓位
    pub fn new(symbol: String, leverage: f64) -> Self {
        Self {
            symbol,
            side: PositionSide::None,
            quantity: 0.0,
            avg_price: Price::new(0.0),
            unrealized_pnl: 0.0,
            realized_pnl: 0.0,
            margin: 0.0,
            leverage,
            open_time: Utc::now(),
            last_updated: Utc::now(),
        }
    }

    /// 检查是否为空仓位
    pub fn is_empty(&self) -> bool {
        self.quantity.abs() < 1e-8
    }

    /// 获取仓位价值
    pub fn notional_value(&self, current_price: Price) -> f64 {
        self.quantity.abs() * current_price.value()
    }

    /// 计算未实现盈亏
    pub fn calculate_unrealized_pnl(&self, current_price: Price) -> f64 {
        if self.is_empty() {
            return 0.0;
        }

        let price_diff = current_price.value() - self.avg_price.value();
        match self.side {
            PositionSide::Long => self.quantity * price_diff,
            PositionSide::Short => -self.quantity * price_diff,
            PositionSide::None => 0.0,
        }
    }

    /// 更新未实现盈亏
    pub fn update_unrealized_pnl(&mut self, current_price: Price) {
        self.unrealized_pnl = self.calculate_unrealized_pnl(current_price);
        self.last_updated = Utc::now();
    }

    /// 计算所需保证金
    pub fn calculate_required_margin(&self, current_price: Price) -> f64 {
        if self.is_empty() {
            return 0.0;
        }
        self.notional_value(current_price) / self.leverage
    }

    /// 更新保证金
    pub fn update_margin(&mut self, current_price: Price) {
        self.margin = self.calculate_required_margin(current_price);
        self.last_updated = Utc::now();
    }

    /// 处理交易（开仓或平仓）
    pub fn process_trade(&mut self, trade: &TradeRecord, current_price: Price) -> Result<f64, String> {
        let trade_quantity = match trade.side {
            OrderSide::Buy => trade.quantity,
            OrderSide::Sell => -trade.quantity,
        };

        let old_quantity = self.quantity;
        let new_quantity = old_quantity + trade_quantity;

        // 计算已实现盈亏
        let realized_pnl = if old_quantity != 0.0 && (old_quantity > 0.0) != (new_quantity > 0.0) {
            // 发生了平仓
            let closed_quantity = if new_quantity.abs() < old_quantity.abs() {
                // 部分平仓
                trade_quantity.abs()
            } else {
                // 完全平仓并可能反向开仓
                old_quantity.abs()
            };

            let price_diff = trade.price.value() - self.avg_price.value();
            let pnl = match self.side {
                PositionSide::Long => closed_quantity * price_diff,
                PositionSide::Short => -closed_quantity * price_diff,
                PositionSide::None => 0.0,
            };
            
            self.realized_pnl += pnl;
            pnl
        } else {
            0.0
        };

        // 更新仓位
        if new_quantity.abs() < 1e-8 {
            // 完全平仓
            self.quantity = 0.0;
            self.side = PositionSide::None;
            self.avg_price = Price::new(0.0);
            self.margin = 0.0;
        } else {
            // 更新平均价格
            if old_quantity == 0.0 {
                // 新开仓
                self.avg_price = trade.price;
                self.open_time = trade.timestamp;
            } else if (old_quantity > 0.0) == (trade_quantity > 0.0) {
                // 加仓
                let total_cost = old_quantity * self.avg_price.value() + trade_quantity * trade.price.value();
                self.avg_price = Price::new(total_cost / new_quantity);
            }
            // 如果是平仓，平均价格不变

            self.quantity = new_quantity;
            self.side = if new_quantity > 0.0 {
                PositionSide::Long
            } else {
                PositionSide::Short
            };
        }

        // 更新其他字段
        self.update_unrealized_pnl(current_price);
        self.update_margin(current_price);
        self.last_updated = Utc::now();

        Ok(realized_pnl)
    }

    /// 获取仓位风险度
    pub fn get_risk_ratio(&self, current_price: Price, account_balance: f64) -> f64 {
        if account_balance <= 0.0 || self.is_empty() {
            return 0.0;
        }
        
        let position_value = self.notional_value(current_price);
        position_value / account_balance
    }

    /// 检查是否需要强制平仓
    pub fn should_liquidate(&self, current_price: Price, liquidation_ratio: f64) -> bool {
        if self.is_empty() || self.margin <= 0.0 {
            return false;
        }

        let unrealized_pnl = self.calculate_unrealized_pnl(current_price);
        let equity = self.margin + unrealized_pnl;
        
        equity / self.margin < liquidation_ratio
    }

    /// 获取仓位摘要信息
    pub fn get_summary(&self, current_price: Price) -> PositionSummary {
        PositionSummary {
            symbol: self.symbol.clone(),
            side: self.side.clone(),
            quantity: self.quantity,
            avg_price: self.avg_price,
            current_price,
            unrealized_pnl: self.calculate_unrealized_pnl(current_price),
            realized_pnl: self.realized_pnl,
            margin: self.margin,
            leverage: self.leverage,
            notional_value: self.notional_value(current_price),
            return_rate: if self.margin > 0.0 {
                self.calculate_unrealized_pnl(current_price) / self.margin
            } else {
                0.0
            },
        }
    }
}

/// 仓位摘要信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PositionSummary {
    pub symbol: String,
    pub side: PositionSide,
    pub quantity: f64,
    pub avg_price: Price,
    pub current_price: Price,
    pub unrealized_pnl: f64,
    pub realized_pnl: f64,
    pub margin: f64,
    pub leverage: f64,
    pub notional_value: f64,
    pub return_rate: f64,
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::account::types::TradeRecord;

    #[test]
    fn test_position_creation() {
        let position = Position::new("BTCUSDT".to_string(), 10.0);
        assert_eq!(position.symbol, "BTCUSDT");
        assert_eq!(position.side, PositionSide::None);
        assert_eq!(position.quantity, 0.0);
        assert_eq!(position.leverage, 10.0);
        assert!(position.is_empty());
    }

    #[test]
    fn test_position_long_trade() {
        let mut position = Position::new("BTCUSDT".to_string(), 10.0);
        let current_price = Price::new(50000.0);
        
        // 开多仓
        let trade = TradeRecord::new(
            "trade1".to_string(),
            "order1".to_string(),
            "BTCUSDT".to_string(),
            OrderSide::Buy,
            Price::new(50000.0),
            1.0,
            10.0,
            "USDT".to_string(),
            false,
        );

        let realized_pnl = position.process_trade(&trade, current_price).unwrap();
        assert_eq!(realized_pnl, 0.0); // 开仓无已实现盈亏
        assert_eq!(position.side, PositionSide::Long);
        assert_eq!(position.quantity, 1.0);
        assert_eq!(position.avg_price.value(), 50000.0);
    }

    #[test]
    fn test_position_unrealized_pnl() {
        let mut position = Position::new("BTCUSDT".to_string(), 10.0);
        position.side = PositionSide::Long;
        position.quantity = 1.0;
        position.avg_price = Price::new(50000.0);

        // 价格上涨
        let current_price = Price::new(55000.0);
        let pnl = position.calculate_unrealized_pnl(current_price);
        assert_eq!(pnl, 5000.0);

        // 价格下跌
        let current_price = Price::new(45000.0);
        let pnl = position.calculate_unrealized_pnl(current_price);
        assert_eq!(pnl, -5000.0);
    }
}
