#!/bin/bash

# 回测框架打包脚本
# 此脚本将用户需要的所有文件打包到压缩包中，让用户解压后可以直接使用

set -e

# 打印消息函数
print_info() {
    echo "[INFO] $1"
}

print_success() {
    echo "[SUCCESS] $1"
}

print_warning() {
    echo "[WARNING] $1"
}

print_error() {
    echo "[ERROR] $1"
}

# 检查必要的工具
check_dependencies() {
    print_info "检查依赖工具..."

    if ! command -v cargo &> /dev/null; then
        print_error "cargo 未找到，请安装 Rust"
        exit 1
    fi

    if ! command -v tar &> /dev/null; then
        print_error "tar 未找到"
        exit 1
    fi

    print_success "依赖检查完成"
}

# 编译 release 版本
build_release() {
    print_info "编译 release 版本..."

    # 编译主项目
    cargo build --release
    if [ $? -ne 0 ]; then
        print_error "主项目编译失败"
        exit 1
    fi

    # 编译工具
    print_info "编译测试工具..."
    cd tools
    cargo build --release
    if [ $? -ne 0 ]; then
        print_error "工具编译失败"
        exit 1
    fi
    cd ..

    print_success "编译完成"
}

# 创建打包目录
create_package_dir() {
    # 获取 git commit hash
    local git_hash=""
    if command -v git &> /dev/null && git rev-parse --git-dir > /dev/null 2>&1; then
        git_hash="-$(git rev-parse --short HEAD)"
    fi

    local package_name="backtest-framework-$(date +%Y%m%d-%H%M%S)${git_hash}"
    local package_dir="./package/$package_name"

    print_info "创建打包目录: $package_dir" >&2

    # 清理并创建目录
    rm -rf ./package
    mkdir -p "$package_dir"

    echo "$package_dir"
}

# 复制必要文件
copy_files() {
    local package_dir="$1"

    print_info "复制文件到打包目录..."

    # 复制文档文件
    print_info "复制文档文件..."
    cp README.md "$package_dir/"
    cp QUICK_START.md "$package_dir/"

    # 复制配置文件
    print_info "复制配置文件..."
    cp example_config.toml "$package_dir/"

    # 复制 TLS 设置脚本
    print_info "复制 TLS 设置脚本..."
    cp setup_tls.sh "$package_dir/"
    cp generate_ca_certs.sh "$package_dir/"

    # 确保脚本可执行
    chmod +x "$package_dir"/*.sh

    # 复制二进制文件
    print_info "复制二进制文件..."
    mkdir -p "$package_dir/bin"

    # 主程序
    if [ -f "./target/release/backtest" ]; then
        cp "./target/release/backtest" "$package_dir/bin/"
    else
        print_warning "主程序二进制文件未找到，请先运行 cargo build --release"
    fi

    # 创建数据目录（用户需要自己准备数据）
    print_info "创建数据目录..."
    mkdir -p "$package_dir/data"

    # 创建 certs 目录（空的，用户运行 setup_tls.sh 时会生成）
    print_info "创建证书目录..."
    mkdir -p "$package_dir/certs"

    print_success "文件复制完成"
}





# 创建压缩包
create_archive() {
    local package_dir="$1"
    local archive_name="$(basename "$package_dir").tar.gz"

    print_info "创建压缩包: $archive_name" >&2

    cd ./package
    tar -czf "$archive_name" "$(basename "$package_dir")"
    cd ..

    # 移动压缩包到当前目录
    mv "./package/$archive_name" "./"

    print_success "压缩包创建完成: $archive_name" >&2

    # 显示压缩包信息
    local size=$(du -h "$archive_name" | cut -f1)
    print_info "压缩包大小: $size" >&2

    echo "$archive_name"
}

# 清理临时文件
cleanup() {
    print_info "清理临时文件..."
    rm -rf ./package
    print_success "清理完成"
}

# 主函数
main() {
    echo "========================================"
    echo "    回测框架打包脚本"
    echo "========================================"
    echo ""

    # 检查依赖
    check_dependencies

    # 编译 release 版本
    build_release

    # 创建打包目录
    package_dir=$(create_package_dir)

    # 复制文件
    copy_files "$package_dir"

    # 创建压缩包
    archive_name=$(create_archive "$package_dir")

    # 清理临时文件
    cleanup

    echo ""
    echo "========================================"
    print_success "打包完成！"
    echo "========================================"
    echo ""
    print_info "压缩包文件: $archive_name"
    print_info "用户可以解压后直接使用："
    echo ""
    echo "  tar -xzf $archive_name"
    echo "  cd $(basename "$package_dir")"
    echo "  ./start.sh"
    echo ""
    print_info "或者使用 TLS 模式："
    echo ""
    echo "  sudo ./setup_tls.sh"
    echo "  ./start_tls.sh"
    echo ""
    print_success "打包脚本执行完成！"
}

# 运行主函数
main "$@"
