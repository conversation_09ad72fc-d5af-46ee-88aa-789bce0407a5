use crate::indicators::calculator::IndicatorCalculator;
use crate::indicators::types::{IndicatorConfig, IndicatorResult, IndicatorType, PriceData};
use crate::types::MarketData;
use crate::Result;
use std::collections::HashMap;
use tokio::sync::broadcast;
use tokio::time::{interval, Duration};
use tracing::{debug, error, info, warn};

/// 指标管理器
pub struct IndicatorManager {
    /// 指标配置
    configs: HashMap<IndicatorType, IndicatorConfig>,
    /// 指标计算器
    calculator: IndicatorCalculator,
    /// 市场数据接收器
    market_data_rx: broadcast::Receiver<MarketData>,
    /// 指标结果发送器
    indicator_tx: broadcast::Sender<IndicatorResult>,
    /// 当前价格数据
    current_price: Option<PriceData>,
}

impl IndicatorManager {
    /// 创建新的指标管理器
    pub fn new(
        market_data_rx: broadcast::Receiver<MarketData>,
        indicator_tx: broadcast::Sender<IndicatorResult>,
    ) -> Self {
        Self {
            configs: HashMap::new(),
            calculator: IndicatorCalculator::new(1000), // 保留1000个历史数据点
            market_data_rx,
            indicator_tx,
            current_price: None,
        }
    }

    /// 添加指标配置
    pub fn add_indicator(&mut self, config: IndicatorConfig) {
        info!("Adding indicator: {:?}", config.indicator_type);
        self.configs.insert(config.indicator_type.clone(), config);
    }

    /// 移除指标配置
    pub fn remove_indicator(&mut self, indicator_type: &IndicatorType) {
        if self.configs.remove(indicator_type).is_some() {
            info!("Removed indicator: {:?}", indicator_type);
        } else {
            warn!(
                "Attempted to remove non-existent indicator: {:?}",
                indicator_type
            );
        }
    }

    /// 启用/禁用指标
    pub fn set_indicator_enabled(&mut self, indicator_type: &IndicatorType, enabled: bool) {
        if let Some(config) = self.configs.get_mut(indicator_type) {
            config.enabled = enabled;
            info!("Set indicator {:?} enabled: {}", indicator_type, enabled);
        } else {
            warn!(
                "Attempted to modify non-existent indicator: {:?}",
                indicator_type
            );
        }
    }

    /// 启动指标管理器
    pub async fn start(self) -> Result<()> {
        info!("Starting indicator manager");

        // 分离组件以避免借用冲突
        let mut market_data_rx = self.market_data_rx;
        let configs = self.configs;
        let indicator_tx = self.indicator_tx;

        // 启动定时计算任务
        let calculation_task = {
            let _configs = configs.clone();
            let _indicator_tx = indicator_tx.clone();

            tokio::spawn(async move {
                let mut interval = interval(Duration::from_millis(100));
                loop {
                    interval.tick().await;
                    // 这里可以添加定时计算逻辑
                }
            })
        };

        // 启动市场数据处理任务
        let data_processing_task = {
            let _configs = configs.clone();
            let _indicator_tx = indicator_tx.clone();

            tokio::spawn(async move {
                while let Ok(market_data) = market_data_rx.recv().await {
                    // 处理市场数据
                    match market_data {
                        MarketData::Trade(trade) => {
                            if let Some(timestamp) = trade.timestamp {
                                let price_data = PriceData::new(
                                    timestamp,
                                    trade.price.into(),
                                    trade.price.into(),
                                    trade.price.into(),
                                    trade.price.into(),
                                    trade.quantity,
                                );
                                // 这里可以添加价格数据处理逻辑
                            }
                        }
                        MarketData::Bbo(bbo) => {
                            let mid_price = (bbo.bid_price + bbo.ask_price) / 2.0;
                            let price_data = PriceData::new(
                                chrono::Utc::now(),
                                mid_price.into(),
                                mid_price.into(),
                                mid_price.into(),
                                mid_price.into(),
                                0.0,
                            );
                            // 这里可以添加价格数据处理逻辑
                        }
                        MarketData::OrderBook(snapshot) => {
                            if let (Some(best_bid), Some(best_ask)) = (
                                snapshot.bids.keys().next_back(),
                                snapshot.asks.keys().next(),
                            ) {
                                let mid_price = (*best_bid + *best_ask) / 2.0;
                                let price_data = PriceData::new(
                                    snapshot.timestamp,
                                    mid_price.into(),
                                    mid_price.into(),
                                    mid_price.into(),
                                    mid_price.into(),
                                    0.0,
                                );
                                // 这里可以添加价格数据处理逻辑
                            }
                        }
                        MarketData::BookTicker(bookticker) => {
                            // 处理BookTicker数据
                            let mid_price = bookticker.mid_price();
                            let price_data = PriceData::new(
                                bookticker.event_datetime(),
                                mid_price.into(),
                                mid_price.into(),
                                mid_price.into(),
                                mid_price.into(),
                                0.0, // BookTicker没有成交量
                            );
                            // 这里可以添加价格数据处理逻辑
                        }

                        MarketData::TradeData(trade_data) => {
                            // 处理TradeData数据
                            let price_data = PriceData::new(
                                trade_data.timestamp_datetime(),
                                trade_data.price.value().into(),
                                trade_data.price.value().into(),
                                trade_data.price.value().into(),
                                trade_data.price.value().into(),
                                trade_data.amount,
                            );
                            // 这里可以添加价格数据处理逻辑
                        }
                    }
                }
            })
        };

        // 等待任务完成
        tokio::select! {
            result = calculation_task => {
                if let Err(e) = result {
                    error!("Calculation task failed: {}", e);
                }
            }
            result = data_processing_task => {
                if let Err(e) = result {
                    error!("Data processing task failed: {}", e);
                }
            }
        }

        info!("Indicator manager stopped");
        Ok(())
    }

    /// 启动计算循环
    async fn start_calculation_loop(&mut self) -> Result<()> {
        let mut interval = interval(Duration::from_millis(100)); // 100ms间隔

        loop {
            interval.tick().await;

            if let Err(e) = self.calculate_all_indicators().await {
                error!("Failed to calculate indicators: {}", e);
            }
        }
    }

    /// 启动数据处理
    async fn start_data_processing(&mut self) -> Result<()> {
        while let Ok(market_data) = self.market_data_rx.recv().await {
            if let Err(e) = self.process_market_data(market_data).await {
                error!("Failed to process market data: {}", e);
            }
        }

        Ok(())
    }

    /// 处理市场数据
    async fn process_market_data(&mut self, market_data: MarketData) -> Result<()> {
        match market_data {
            MarketData::Trade(trade) => {
                // 从交易数据构建价格数据
                if let Some(timestamp) = trade.timestamp {
                    let price_data = PriceData::new(
                        timestamp,
                        trade.price.into(), // 使用交易价格作为OHLC
                        trade.price.into(),
                        trade.price.into(),
                        trade.price.into(),
                        trade.quantity,
                    );

                    self.update_price_data(price_data);
                }
            }
            MarketData::OrderBook(snapshot) => {
                // 从订单簿快照构建价格数据
                if let (Some(best_bid), Some(best_ask)) = (
                    snapshot.bids.keys().next_back(),
                    snapshot.asks.keys().next(),
                ) {
                    let mid_price = (*best_bid + *best_ask) / 2.0;
                    let price_data = PriceData::new(
                        snapshot.timestamp,
                        mid_price.into(),
                        mid_price.into(),
                        mid_price.into(),
                        mid_price.into(),
                        0.0, // 订单簿没有成交量信息
                    );

                    self.update_price_data(price_data);
                }
            }
            MarketData::Bbo(bbo) => {
                // 从BBO构建价格数据
                let mid_price = (bbo.bid_price + bbo.ask_price) / 2.0;
                let price_data = PriceData::new(
                    chrono::Utc::now(), // BBO没有时间戳，使用当前时间
                    mid_price.into(),
                    mid_price.into(),
                    mid_price.into(),
                    mid_price.into(),
                    0.0,
                );

                self.update_price_data(price_data);
            }
            MarketData::BookTicker(bookticker) => {
                // 从BookTicker数据构建价格数据
                let mid_price = bookticker.mid_price();
                let price_data = PriceData::new(
                    bookticker.event_datetime(),
                    mid_price.into(),
                    mid_price.into(),
                    mid_price.into(),
                    mid_price.into(),
                    0.0, // BookTicker没有成交量
                );

                self.update_price_data(price_data);
            }

            MarketData::TradeData(trade_data) => {
                // 从TradeData数据构建价格数据
                let price_data = PriceData::new(
                    trade_data.timestamp_datetime(),
                    trade_data.price.value().into(),
                    trade_data.price.value().into(),
                    trade_data.price.value().into(),
                    trade_data.price.value().into(),
                    trade_data.amount,
                );

                self.update_price_data(price_data);
            }
        }

        Ok(())
    }

    /// 更新价格数据
    fn update_price_data(&mut self, price_data: PriceData) {
        debug!("Updating price data: close={}", price_data.close);
        self.calculator.add_price_data(price_data.clone());
        self.current_price = Some(price_data);
    }

    /// 计算所有启用的指标
    async fn calculate_all_indicators(&mut self) -> Result<()> {
        if self.current_price.is_none() {
            return Ok(()); // 没有价格数据，跳过计算
        }

        let current_time = chrono::Utc::now();

        for (indicator_type, config) in &self.configs {
            if !config.enabled {
                continue;
            }

            // 检查是否需要更新（基于更新间隔）
            // 这里简化了时间检查逻辑

            match self.calculator.calculate(indicator_type) {
                Ok(value) => {
                    let result = IndicatorResult {
                        indicator_type: indicator_type.clone(),
                        value,
                        timestamp: current_time,
                    };

                    if let Err(e) = self.indicator_tx.send(result) {
                        error!("Failed to send indicator result: {}", e);
                    } else {
                        debug!("Calculated indicator: {:?}", indicator_type);
                    }
                }
                Err(e) => {
                    debug!("Failed to calculate indicator {:?}: {}", indicator_type, e);
                }
            }
        }

        Ok(())
    }

    /// 获取指标配置
    pub fn get_config(&self, indicator_type: &IndicatorType) -> Option<&IndicatorConfig> {
        self.configs.get(indicator_type)
    }

    /// 获取所有指标配置
    pub fn get_all_configs(&self) -> &HashMap<IndicatorType, IndicatorConfig> {
        &self.configs
    }

    /// 获取当前价格数据
    pub fn get_current_price(&self) -> Option<&PriceData> {
        self.current_price.as_ref()
    }

    /// 获取历史数据长度
    pub fn history_length(&self) -> usize {
        self.calculator.history_length()
    }

    /// 清空历史数据
    pub fn clear_history(&mut self) {
        self.calculator.clear_history();
        self.current_price = None;
        info!("Cleared indicator history");
    }

    /// 手动计算指定指标
    pub fn calculate_indicator(&self, indicator_type: &IndicatorType) -> Result<IndicatorResult> {
        let value = self.calculator.calculate(indicator_type)?;
        Ok(IndicatorResult {
            indicator_type: indicator_type.clone(),
            value,
            timestamp: chrono::Utc::now(),
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::types::{OrderSide, Price, Trade};
    use tokio::sync::broadcast;

    #[tokio::test]
    async fn test_indicator_manager() {
        let (market_tx, market_rx) = broadcast::channel(100);
        let (indicator_tx, _indicator_rx) = broadcast::channel(100);

        let mut manager = IndicatorManager::new(market_rx, indicator_tx);

        // 添加SMA指标
        let config = IndicatorConfig {
            indicator_type: IndicatorType::SMA(5),
            enabled: true,
            update_interval_ms: 1000,
        };
        manager.add_indicator(config);

        // 发送测试交易数据
        let trade = Trade {
            id: "test1".to_string(),
            price: Price::new(100.0),
            quantity: 10.0,
            side: OrderSide::Buy,
            timestamp: Some(chrono::Utc::now()),
        };

        let market_data = MarketData::Trade(trade);
        market_tx.send(market_data).unwrap();

        // 验证管理器状态
        assert_eq!(manager.get_all_configs().len(), 1);
        assert!(manager.get_config(&IndicatorType::SMA(5)).is_some());
    }
}
