use crate::types::{Order, OrderSide, Price, Quantity};
use std::collections::BTreeMap;

/// 订单簿实现
#[derive(<PERSON>bu<PERSON>, <PERSON>lone)]
pub struct OrderBook {
    /// 买单队列 (价格从高到低)
    bids: BTreeMap<Price, Vec<Order>>,
    /// 卖单队列 (价格从低到高)
    asks: BTreeMap<Price, Vec<Order>>,
}

impl OrderBook {
    /// 创建新的订单簿
    pub fn new() -> Self {
        Self {
            bids: BTreeMap::new(),
            asks: BTreeMap::new(),
        }
    }

    /// 添加订单到订单簿
    pub fn add_order(&mut self, order: Order) {
        match order.side {
            OrderSide::Buy => {
                self.bids
                    .entry(order.price.unwrap_or(Price::new(0.0)))
                    .or_insert_with(Vec::new)
                    .push(order);
            }
            OrderSide::Sell => {
                self.asks
                    .entry(order.price.unwrap_or(Price::new(0.0)))
                    .or_insert_with(Vec::new)
                    .push(order);
            }
        }
    }

    /// 移除订单
    pub fn remove_order(&mut self, order_id: &str) -> Option<Order> {
        // 在买单中查找
        let mut price_to_remove = None;
        let mut found_order = None;

        for (price, orders) in self.bids.iter_mut() {
            if let Some(pos) = orders.iter().position(|o| o.id == order_id) {
                let order = orders.remove(pos);
                if orders.is_empty() {
                    price_to_remove = Some(*price);
                }
                found_order = Some(order);
                break;
            }
        }

        if let Some(price) = price_to_remove {
            self.bids.remove(&price);
        }

        if found_order.is_some() {
            return found_order;
        }

        // 在卖单中查找
        price_to_remove = None;

        for (price, orders) in self.asks.iter_mut() {
            if let Some(pos) = orders.iter().position(|o| o.id == order_id) {
                let order = orders.remove(pos);
                if orders.is_empty() {
                    price_to_remove = Some(*price);
                }
                found_order = Some(order);
                break;
            }
        }

        if let Some(price) = price_to_remove {
            self.asks.remove(&price);
        }

        found_order
    }

    /// 获取最佳买价
    pub fn best_bid(&self) -> Option<Price> {
        self.bids.keys().next_back().copied()
    }

    /// 获取最佳卖价
    pub fn best_ask(&self) -> Option<Price> {
        self.asks.keys().next().copied()
    }

    /// 获取买单深度
    pub fn bid_depth(&self, price: Price) -> Quantity {
        self.bids
            .get(&price)
            .map(|orders| orders.iter().map(|o| o.quantity).sum())
            .unwrap_or(0.0)
    }

    /// 获取卖单深度
    pub fn ask_depth(&self, price: Price) -> Quantity {
        self.asks
            .get(&price)
            .map(|orders| orders.iter().map(|o| o.quantity).sum())
            .unwrap_or(0.0)
    }

    /// 获取所有买单价格层级
    pub fn bid_levels(&self) -> Vec<(Price, Quantity)> {
        self.bids
            .iter()
            .rev() // 从高到低
            .map(|(price, orders)| {
                let total_quantity = orders.iter().map(|o| o.quantity).sum();
                (*price, total_quantity)
            })
            .collect()
    }

    /// 获取所有卖单价格层级
    pub fn ask_levels(&self) -> Vec<(Price, Quantity)> {
        self.asks
            .iter()
            .map(|(price, orders)| {
                let total_quantity = orders.iter().map(|o| o.quantity).sum();
                (*price, total_quantity)
            })
            .collect()
    }

    /// 清空订单簿
    pub fn clear(&mut self) {
        self.bids.clear();
        self.asks.clear();
    }

    /// 从快照重建订单簿
    pub fn rebuild_from_snapshot(
        &mut self,
        bids: &BTreeMap<Price, Quantity>,
        asks: &BTreeMap<Price, Quantity>,
    ) {
        self.clear();

        // 重建买单深度 - 创建虚拟订单来表示市场深度
        for (price, quantity) in bids {
            if *quantity > 0.0 {
                let virtual_order = Order {
                    id: format!(
                        "virtual_bid_{}_{}",
                        price.value(),
                        chrono::Utc::now().timestamp_nanos_opt().unwrap_or(0)
                    ),
                    order_type: crate::types::OrderType::Limit,
                    side: crate::types::OrderSide::Buy,
                    price: Some(*price),
                    quantity: *quantity,
                    status: crate::types::OrderStatus::Pending,
                    timestamp: chrono::Utc::now(),
                };
                self.bids
                    .entry(*price)
                    .or_insert_with(Vec::new)
                    .push(virtual_order);
            }
        }

        // 重建卖单深度 - 创建虚拟订单来表示市场深度
        for (price, quantity) in asks {
            if *quantity > 0.0 {
                let virtual_order = Order {
                    id: format!(
                        "virtual_ask_{}_{}",
                        price.value(),
                        chrono::Utc::now().timestamp_nanos_opt().unwrap_or(0)
                    ),
                    order_type: crate::types::OrderType::Limit,
                    side: crate::types::OrderSide::Sell,
                    price: Some(*price),
                    quantity: *quantity,
                    status: crate::types::OrderStatus::Pending,
                    timestamp: chrono::Utc::now(),
                };
                self.asks
                    .entry(*price)
                    .or_insert_with(Vec::new)
                    .push(virtual_order);
            }
        }
    }

    /// 获取订单簿状态快照
    pub fn snapshot(&self) -> (BTreeMap<Price, Quantity>, BTreeMap<Price, Quantity>) {
        let bids = self
            .bids
            .iter()
            .map(|(price, orders)| {
                let total_quantity = orders.iter().map(|o| o.quantity).sum();
                (*price, total_quantity)
            })
            .collect();

        let asks = self
            .asks
            .iter()
            .map(|(price, orders)| {
                let total_quantity = orders.iter().map(|o| o.quantity).sum();
                (*price, total_quantity)
            })
            .collect();

        (bids, asks)
    }

    /// 检查订单簿是否为空
    pub fn is_empty(&self) -> bool {
        self.bids.is_empty() && self.asks.is_empty()
    }

    /// 获取买单总数
    pub fn bid_count(&self) -> usize {
        self.bids.values().map(|orders| orders.len()).sum()
    }

    /// 获取卖单总数
    pub fn ask_count(&self) -> usize {
        self.asks.values().map(|orders| orders.len()).sum()
    }

    /// FIFO撮合 - 尝试撮合新订单与订单簿中的订单
    /// 返回 (成交记录列表, 剩余订单数量)
    pub fn match_order(
        &mut self,
        incoming_order: &Order,
    ) -> Result<(Vec<(Order, Price, Quantity)>, Quantity), String> {
        let mut matches = Vec::new();
        let mut remaining_quantity = incoming_order.quantity;

        let order_price = incoming_order
            .price
            .ok_or("Order must have a price for matching")?;

        match incoming_order.side {
            OrderSide::Buy => {
                // 买单与卖单撮合，按价格从低到高撮合
                let mut prices_to_remove = Vec::new();

                for (price, orders) in self.asks.iter_mut() {
                    if *price <= order_price && remaining_quantity > 0.0 {
                        let mut orders_to_remove = Vec::new();

                        // FIFO: 按时间顺序撮合同价格订单
                        for (index, order) in orders.iter_mut().enumerate() {
                            if remaining_quantity <= 0.0 {
                                break;
                            }

                            let match_quantity = remaining_quantity.min(order.quantity);
                            matches.push((order.clone(), *price, match_quantity));

                            order.quantity -= match_quantity;
                            remaining_quantity -= match_quantity;

                            if order.quantity <= 0.0 {
                                orders_to_remove.push(index);
                            }
                        }

                        // 移除已完全成交的订单（从后往前移除以保持索引有效）
                        for &index in orders_to_remove.iter().rev() {
                            orders.remove(index);
                        }

                        if orders.is_empty() {
                            prices_to_remove.push(*price);
                        }
                    } else {
                        break; // 价格不匹配，停止撮合
                    }
                }

                // 移除空的价格层级
                for price in prices_to_remove {
                    self.asks.remove(&price);
                }
            }
            OrderSide::Sell => {
                // 卖单与买单撮合，按价格从高到低撮合
                let mut prices_to_remove = Vec::new();

                // 需要反向迭代买单（从高价到低价）
                let bid_prices: Vec<Price> = self.bids.keys().rev().cloned().collect();

                for price in bid_prices {
                    if price >= order_price && remaining_quantity > 0.0 {
                        if let Some(orders) = self.bids.get_mut(&price) {
                            let mut orders_to_remove = Vec::new();

                            // FIFO: 按时间顺序撮合同价格订单
                            for (index, order) in orders.iter_mut().enumerate() {
                                if remaining_quantity <= 0.0 {
                                    break;
                                }

                                let match_quantity = remaining_quantity.min(order.quantity);
                                matches.push((order.clone(), price, match_quantity));

                                order.quantity -= match_quantity;
                                remaining_quantity -= match_quantity;

                                if order.quantity <= 0.0 {
                                    orders_to_remove.push(index);
                                }
                            }

                            // 移除已完全成交的订单（从后往前移除以保持索引有效）
                            for &index in orders_to_remove.iter().rev() {
                                orders.remove(index);
                            }

                            if orders.is_empty() {
                                prices_to_remove.push(price);
                            }
                        }
                    } else {
                        break; // 价格不匹配，停止撮合
                    }
                }

                // 移除空的价格层级
                for price in prices_to_remove {
                    self.bids.remove(&price);
                }
            }
        }

        Ok((matches, remaining_quantity))
    }

    /// 更新订单数量（用于部分成交）
    pub fn update_order_quantity(&mut self, order_id: &str, new_quantity: Quantity) -> bool {
        // 在买单中查找并更新
        for orders in self.bids.values_mut() {
            if let Some(order) = orders.iter_mut().find(|o| o.id == order_id) {
                order.quantity = new_quantity;
                return true;
            }
        }

        // 在卖单中查找并更新
        for orders in self.asks.values_mut() {
            if let Some(order) = orders.iter_mut().find(|o| o.id == order_id) {
                order.quantity = new_quantity;
                return true;
            }
        }

        false
    }
}

impl Default for OrderBook {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::types::{OrderStatus, OrderType};
    use chrono::Utc;

    #[test]
    fn test_orderbook_operations() {
        let mut orderbook = OrderBook::new();

        // 创建测试订单
        let buy_order = Order {
            id: "buy1".to_string(),
            order_type: OrderType::Limit,
            side: OrderSide::Buy,
            price: Some(Price::new(99.5)),
            quantity: 10.0,
            status: OrderStatus::Pending,
            timestamp: Utc::now(),
        };

        let sell_order = Order {
            id: "sell1".to_string(),
            order_type: OrderType::Limit,
            side: OrderSide::Sell,
            price: Some(Price::new(100.5)),
            quantity: 15.0,
            status: OrderStatus::Pending,
            timestamp: Utc::now(),
        };

        // 添加订单
        orderbook.add_order(buy_order);
        orderbook.add_order(sell_order);

        // 验证最佳价格
        assert_eq!(orderbook.best_bid(), Some(Price::new(99.5)));
        assert_eq!(orderbook.best_ask(), Some(Price::new(100.5)));

        // 验证深度
        assert_eq!(orderbook.bid_depth(Price::new(99.5)), 10.0);
        assert_eq!(orderbook.ask_depth(Price::new(100.5)), 15.0);

        // 移除订单
        let removed = orderbook.remove_order("buy1");
        assert!(removed.is_some());
        assert_eq!(orderbook.best_bid(), None);
    }

    #[test]
    fn test_orderbook_fifo_matching() {
        let mut orderbook = OrderBook::new();

        // 添加多个相同价格的卖单（测试FIFO）
        let sell_order1 = Order {
            id: "sell1".to_string(),
            order_type: OrderType::Limit,
            side: OrderSide::Sell,
            price: Some(Price::new(100.0)),
            quantity: 5.0,
            status: OrderStatus::Pending,
            timestamp: Utc::now(),
        };

        let sell_order2 = Order {
            id: "sell2".to_string(),
            order_type: OrderType::Limit,
            side: OrderSide::Sell,
            price: Some(Price::new(100.0)),
            quantity: 3.0,
            status: OrderStatus::Pending,
            timestamp: Utc::now(),
        };

        orderbook.add_order(sell_order1);
        orderbook.add_order(sell_order2);

        // 创建买单进行撮合
        let buy_order = Order {
            id: "buy1".to_string(),
            order_type: OrderType::Limit,
            side: OrderSide::Buy,
            price: Some(Price::new(100.0)),
            quantity: 6.0,
            status: OrderStatus::Pending,
            timestamp: Utc::now(),
        };

        // 执行撮合
        let result = orderbook.match_order(&buy_order);
        assert!(result.is_ok());

        let (matches, remaining_quantity) = result.unwrap();
        assert_eq!(matches.len(), 2); // 应该有两个成交
        assert_eq!(remaining_quantity, 0.0); // 买单应该完全成交

        // 验证FIFO顺序：第一个订单应该完全成交，第二个订单部分成交
        assert_eq!(matches[0].0.id, "sell1"); // 第一个成交的是sell1
        assert_eq!(matches[0].2, 5.0); // sell1完全成交
        assert_eq!(matches[1].0.id, "sell2"); // 第二个成交的是sell2
        assert_eq!(matches[1].2, 1.0); // sell2部分成交

        // 验证订单簿状态
        assert_eq!(orderbook.ask_depth(Price::new(100.0)), 2.0); // 剩余2.0数量
    }

    #[test]
    fn test_orderbook_price_priority() {
        let mut orderbook = OrderBook::new();

        // 添加不同价格的卖单
        let sell_order1 = Order {
            id: "sell1".to_string(),
            order_type: OrderType::Limit,
            side: OrderSide::Sell,
            price: Some(Price::new(101.0)),
            quantity: 5.0,
            status: OrderStatus::Pending,
            timestamp: Utc::now(),
        };

        let sell_order2 = Order {
            id: "sell2".to_string(),
            order_type: OrderType::Limit,
            side: OrderSide::Sell,
            price: Some(Price::new(100.0)),
            quantity: 3.0,
            status: OrderStatus::Pending,
            timestamp: Utc::now(),
        };

        orderbook.add_order(sell_order1);
        orderbook.add_order(sell_order2);

        // 创建买单进行撮合
        let buy_order = Order {
            id: "buy1".to_string(),
            order_type: OrderType::Limit,
            side: OrderSide::Buy,
            price: Some(Price::new(101.0)),
            quantity: 6.0,
            status: OrderStatus::Pending,
            timestamp: Utc::now(),
        };

        // 执行撮合
        let result = orderbook.match_order(&buy_order);
        assert!(result.is_ok());

        let (matches, remaining_quantity) = result.unwrap();
        assert_eq!(matches.len(), 2); // 应该有两个成交
        assert_eq!(remaining_quantity, 0.0); // 买单应该完全成交

        // 验证价格优先：应该先撮合100.0价格的订单
        assert_eq!(matches[0].0.id, "sell2"); // 第一个成交的是sell2（价格更低）
        assert_eq!(matches[0].1, Price::new(100.0)); // 成交价格是100.0
        assert_eq!(matches[1].0.id, "sell1"); // 第二个成交的是sell1
        assert_eq!(matches[1].1, Price::new(101.0)); // 成交价格是101.0
    }

    #[test]
    fn test_orderbook_partial_fill() {
        let mut orderbook = OrderBook::new();

        // 添加卖单
        let sell_order = Order {
            id: "sell1".to_string(),
            order_type: OrderType::Limit,
            side: OrderSide::Sell,
            price: Some(Price::new(100.0)),
            quantity: 10.0,
            status: OrderStatus::Pending,
            timestamp: Utc::now(),
        };

        orderbook.add_order(sell_order);

        // 创建较小的买单进行撮合
        let buy_order = Order {
            id: "buy1".to_string(),
            order_type: OrderType::Limit,
            side: OrderSide::Buy,
            price: Some(Price::new(100.0)),
            quantity: 3.0,
            status: OrderStatus::Pending,
            timestamp: Utc::now(),
        };

        // 执行撮合
        let result = orderbook.match_order(&buy_order);
        assert!(result.is_ok());

        let (matches, remaining_quantity) = result.unwrap();
        assert_eq!(matches.len(), 1); // 应该有一个成交
        assert_eq!(remaining_quantity, 0.0); // 买单应该完全成交
        assert_eq!(matches[0].2, 3.0); // 成交数量是3.0

        // 验证订单簿状态：卖单应该还剩7.0数量
        assert_eq!(orderbook.ask_depth(Price::new(100.0)), 7.0);
    }

    #[test]
    fn test_orderbook_rebuild_from_snapshot() {
        let mut orderbook = OrderBook::new();

        // 创建快照数据
        let mut bids = BTreeMap::new();
        let mut asks = BTreeMap::new();

        bids.insert(Price::new(99.0), 10.0);
        bids.insert(Price::new(98.0), 15.0);
        asks.insert(Price::new(101.0), 8.0);
        asks.insert(Price::new(102.0), 12.0);

        // 重建订单簿
        orderbook.rebuild_from_snapshot(&bids, &asks);

        // 验证重建结果
        assert_eq!(orderbook.best_bid(), Some(Price::new(99.0)));
        assert_eq!(orderbook.best_ask(), Some(Price::new(101.0)));
        assert_eq!(orderbook.bid_depth(Price::new(99.0)), 10.0);
        assert_eq!(orderbook.ask_depth(Price::new(101.0)), 8.0);
        assert_eq!(orderbook.bid_count(), 2);
        assert_eq!(orderbook.ask_count(), 2);
    }

    #[test]
    fn test_orderbook_update_order_quantity() {
        let mut orderbook = OrderBook::new();

        // 添加订单
        let order = Order {
            id: "test1".to_string(),
            order_type: OrderType::Limit,
            side: OrderSide::Buy,
            price: Some(Price::new(99.0)),
            quantity: 10.0,
            status: OrderStatus::Pending,
            timestamp: Utc::now(),
        };

        orderbook.add_order(order);

        // 更新订单数量
        let updated = orderbook.update_order_quantity("test1", 5.0);
        assert!(updated);

        // 验证更新结果
        assert_eq!(orderbook.bid_depth(Price::new(99.0)), 5.0);

        // 尝试更新不存在的订单
        let not_updated = orderbook.update_order_quantity("nonexistent", 1.0);
        assert!(!not_updated);
    }
}
