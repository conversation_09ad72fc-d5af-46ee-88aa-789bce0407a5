use crate::data::DataReader;
use crate::types::MarketData;
use crate::{BacktestError, Result};
use std::sync::Arc;
use tokio::sync::{mpsc, Mutex, RwLock};
use tracing::info;

/// 数据流状态
#[derive(Debug, Clone, PartialEq)]
pub enum DataStreamStatus {
    /// 停止状态
    Stopped,
    /// 运行状态
    Running,
    /// 暂停状态
    Paused,
    /// 错误状态
    Error(String),
}

/// 数据流控制配置
#[derive(Debug, Clone)]
pub struct DataStreamConfig {
    /// 数据读取间隔（毫秒）
    pub read_interval_ms: u64,
    /// 是否启用实时模拟
    pub realtime_simulation: bool,
    /// 最大缓冲区大小
    pub buffer_size: usize,
}

impl Default for DataStreamConfig {
    fn default() -> Self {
        Self {
            read_interval_ms: 1000, // 默认1秒间隔
            realtime_simulation: true,
            buffer_size: 1000,
        }
    }
}

/// 数据流统计信息
#[derive(Debug, <PERSON><PERSON>)]
pub struct DataStreamStats {
    /// 已处理的消息数量
    pub messages_processed: u64,
    /// 启动时间
    pub start_time: Option<chrono::DateTime<chrono::Utc>>,
    /// 最后处理时间
    pub last_processed_time: Option<chrono::DateTime<chrono::Utc>>,
    /// 错误计数
    pub error_count: u64,
}

impl Default for DataStreamStats {
    fn default() -> Self {
        Self {
            messages_processed: 0,
            start_time: None,
            last_processed_time: None,
            error_count: 0,
        }
    }
}

/// 数据流控制器
/// 负责管理数据流的状态和配置，直接依赖 DataReader 进行数据读取
pub struct DataStreamController {
    /// 当前状态
    status: Arc<RwLock<DataStreamStatus>>,
    /// 控制配置
    config: Arc<RwLock<DataStreamConfig>>,
    /// 统计信息
    stats: Arc<RwLock<DataStreamStats>>,
    /// 数据输出通道
    output_tx: Arc<RwLock<Option<mpsc::Sender<MarketData>>>>,
    /// 数据读取器 - Controller 直接依赖 Reader
    reader: Arc<Mutex<DataReader>>,
}

impl DataStreamController {
    /// 创建新的数据流控制器，直接依赖 DataReader
    pub fn new() -> Result<Self> {
        let reader = DataReader::new()?;
        Ok(Self {
            status: Arc::new(RwLock::new(DataStreamStatus::Stopped)),
            config: Arc::new(RwLock::new(DataStreamConfig::default())),
            stats: Arc::new(RwLock::new(DataStreamStats::default())),
            output_tx: Arc::new(RwLock::new(None)),
            reader: Arc::new(Mutex::new(reader)),
        })
    }

    /// 使用指定的 Reader 创建控制器（用于依赖注入和测试）
    pub fn with_reader(reader: DataReader) -> Self {
        Self {
            status: Arc::new(RwLock::new(DataStreamStatus::Stopped)),
            config: Arc::new(RwLock::new(DataStreamConfig::default())),
            stats: Arc::new(RwLock::new(DataStreamStats::default())),
            output_tx: Arc::new(RwLock::new(None)),
            reader: Arc::new(Mutex::new(reader)),
        }
    }

    /// 设置输出通道
    pub async fn set_output_channel(&self, output_tx: mpsc::Sender<MarketData>) {
        *self.output_tx.write().await = Some(output_tx);
    }

    pub async fn prepare(&mut self) -> Result<()> {
        self.prepare_data_reading().await
    }

    /// 启动数据流
    pub async fn start(&self) -> Result<()> {
        let current_status = self.status.read().await.clone();
        if current_status == DataStreamStatus::Running {
            return Err(BacktestError::Data(
                "Data stream is already running".to_string(),
            ));
        }

        info!("Starting data stream controller");

        // 更新状态
        *self.status.write().await = DataStreamStatus::Running;
        self.reader.lock().await.start().await?;

        // 更新统计信息
        let mut stats = self.stats.write().await;
        stats.start_time = Some(chrono::Utc::now());

        info!("Data stream controller started successfully");
        Ok(())
    }

    /// 停止数据流
    pub async fn stop(&self) -> Result<()> {
        info!("Stopping data stream controller");

        // 停止Reader
        self.reader.lock().await.stop().await?;

        // 更新状态
        *self.status.write().await = DataStreamStatus::Stopped;

        info!("Data stream controller stopped");
        Ok(())
    }

    /// 暂停数据流
    pub async fn pause(&self) -> Result<()> {
        info!("Pausing data stream");

        // 暂停Reader
        self.reader.lock().await.pause().await?;

        *self.status.write().await = DataStreamStatus::Paused;
        Ok(())
    }

    /// 恢复数据流
    pub async fn resume(&self) -> Result<()> {
        info!("Resuming data stream");

        // 恢复Reader
        self.reader.lock().await.resume().await?;

        *self.status.write().await = DataStreamStatus::Running;
        Ok(())
    }

    /// 更新配置
    pub async fn update_config(&self, new_config: DataStreamConfig) -> Result<()> {
        info!("Updating data stream config");
        *self.config.write().await = new_config;
        Ok(())
    }

    /// 获取当前状态
    pub async fn get_status(&self) -> DataStreamStatus {
        self.status.read().await.clone()
    }

    /// 获取当前配置
    pub async fn get_config(&self) -> DataStreamConfig {
        self.config.read().await.clone()
    }

    /// 获取统计信息
    pub async fn get_stats(&self) -> DataStreamStats {
        self.stats.read().await.clone()
    }

    /// 获取输出通道（用于数据流服务）
    pub async fn get_output_channel(&self) -> Option<mpsc::Sender<MarketData>> {
        self.output_tx.read().await.clone()
    }

    /// 增加处理消息计数
    pub async fn increment_message_count(&self) {
        let mut stats = self.stats.write().await;
        stats.messages_processed += 1;
        stats.last_processed_time = Some(chrono::Utc::now());
    }

    /// 增加错误计数
    pub async fn increment_error_count(&self) {
        let mut stats = self.stats.write().await;
        stats.error_count += 1;
    }

    pub async fn prepare_data_reading(&mut self) -> Result<()> {
        // 获取输出通道
        let output_tx = self
            .get_output_channel()
            .await
            .ok_or_else(|| BacktestError::Data("Output channel not available".to_string()))?;

        // Controller 直接使用其依赖的 Reader 进行数据读取
        self.reader.lock().await.prepare_reading(output_tx).await?;

        // 更新统计信息
        self.increment_message_count().await;

        Ok(())
    }

    /// 获取 Reader 的引用（用于高级用法）
    pub fn get_reader(&self) -> Arc<Mutex<DataReader>> {
        Arc::clone(&self.reader)
    }

    /// 获取Reader的状态
    pub async fn get_reader_status(&self) -> crate::data::reader::DataReaderStatus {
        self.reader.lock().await.get_status().await
    }

    /// 检查Controller和Reader状态是否同步
    pub async fn is_status_synchronized(&self) -> bool {
        let controller_status = self.get_status().await;
        let reader_status = self.reader.lock().await.get_status().await;

        use crate::data::reader::DataReaderStatus;

        match (controller_status, reader_status) {
            (DataStreamStatus::Running, DataReaderStatus::Reading) => true,
            (DataStreamStatus::Paused, DataReaderStatus::Paused) => true,
            (DataStreamStatus::Stopped, DataReaderStatus::Stopped | DataReaderStatus::Idle) => true,
            _ => false,
        }
    }
}
