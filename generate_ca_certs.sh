#!/bin/bash

# 生成CA证书和服务器证书的脚本
# 这样生成的证书可以被客户端验证（如果客户端信任CA证书）

set -e

CERT_DIR="./certs"
CA_KEY="$CERT_DIR/ca.key"
CA_CERT="$CERT_DIR/ca.crt"
SERVER_KEY="$CERT_DIR/server.key"
SERVER_CERT="$CERT_DIR/server.crt"
SERVER_CSR="$CERT_DIR/server.csr"
DAYS=365

echo "Generating CA and server certificates..."

# 创建证书目录
mkdir -p "$CERT_DIR"

# 1. 生成CA私钥
echo "1. Generating CA private key..."
openssl genrsa -out "$CA_KEY" 4096

# 2. 生成CA证书
echo "2. Generating CA certificate..."
openssl req -new -x509 -key "$CA_KEY" -sha256 -subj "/C=US/ST=Test/L=Test/O=Backtest-CA/CN=Backtest-Root-CA" -days "$DAYS" -out "$CA_CERT"

# 3. 生成服务器私钥
echo "3. Generating server private key..."
openssl genrsa -out "$SERVER_KEY" 4096

# 4. 生成服务器证书签名请求
echo "4. Generating server certificate signing request..."
openssl req -new -key "$SERVER_KEY" -out "$SERVER_CSR" -subj "/C=US/ST=Test/L=Test/O=Backtest/CN=localhost"

# 5. 创建扩展文件（包含SAN）
echo "5. Creating certificate extensions..."
cat > "$CERT_DIR/server.ext" << EOF
authorityKeyIdentifier=keyid,issuer
basicConstraints=CA:FALSE
keyUsage = digitalSignature, nonRepudiation, keyEncipherment, dataEncipherment
subjectAltName = @alt_names

[alt_names]
DNS.1 = localhost
DNS.2 = 127.0.0.1
IP.1 = 127.0.0.1
IP.2 = ::1
EOF

# 6. 用CA签名服务器证书
echo "6. Signing server certificate with CA..."
openssl x509 -req -in "$SERVER_CSR" -CA "$CA_CERT" -CAkey "$CA_KEY" -CAcreateserial -out "$SERVER_CERT" -days "$DAYS" -sha256 -extfile "$CERT_DIR/server.ext"

# 7. 设置适当的权限和所有者
chmod 600 "$CA_KEY" "$SERVER_KEY"
chmod 644 "$CA_CERT" "$SERVER_CERT"

# 如果脚本以root身份运行，将文件所有者改为调用sudo的用户
if [[ $EUID -eq 0 ]] && [[ -n "$SUDO_USER" ]]; then
    echo "7.1. Setting file ownership to $SUDO_USER..."
    chown "$SUDO_USER:$SUDO_USER" "$CA_KEY" "$SERVER_KEY" "$CA_CERT" "$SERVER_CERT"
    # 也修改整个证书目录的所有者
    chown "$SUDO_USER:$SUDO_USER" "$CERT_DIR"
fi

# 8. 清理临时文件
rm "$SERVER_CSR" "$CERT_DIR/server.ext" "$CERT_DIR/ca.srl"

echo "Certificates generated successfully:"
echo "  CA Certificate: $CA_CERT"
echo "  CA Private Key: $CA_KEY"
echo "  Server Certificate: $SERVER_CERT"
echo "  Server Private Key: $SERVER_KEY"
echo ""
echo "To use these certificates:"
echo "1. Update your config.toml to use the new server certificate and key"
echo "2. For clients to trust the server, they need to trust the CA certificate"
echo ""
echo "Client configuration options:"
echo "1. Add CA certificate to client's trust store"
echo "2. Or configure client to use the CA certificate for verification"
echo "3. Or disable certificate verification (development only)"
echo ""
echo "Example client configuration (Rust):"
echo "  let mut connector = native_tls::TlsConnector::builder();"
echo "  let ca_cert = std::fs::read(\"$CA_CERT\")?;"
echo "  let ca_cert = native_tls::Certificate::from_pem(&ca_cert)?;"
echo "  connector.add_root_certificate(ca_cert);"
echo "  let connector = connector.build()?;"
