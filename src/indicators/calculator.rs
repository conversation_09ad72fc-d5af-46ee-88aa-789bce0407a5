use crate::indicators::types::{IndicatorType, IndicatorValue, PriceData};
use crate::{BacktestError, Result};
use std::collections::{HashMap, VecDeque};

/// 技术指标计算器
pub struct IndicatorCalculator {
    /// 价格数据缓存
    price_history: VecDeque<PriceData>,
    /// 最大历史数据长度
    max_history: usize,
}

impl IndicatorCalculator {
    /// 创建新的指标计算器
    pub fn new(max_history: usize) -> Self {
        Self {
            price_history: VecDeque::with_capacity(max_history),
            max_history,
        }
    }
    
    /// 添加价格数据
    pub fn add_price_data(&mut self, price_data: PriceData) {
        self.price_history.push_back(price_data);
        
        // 保持历史数据在限制范围内
        while self.price_history.len() > self.max_history {
            self.price_history.pop_front();
        }
    }
    
    /// 计算指定类型的技术指标
    pub fn calculate(&self, indicator_type: &IndicatorType) -> Result<IndicatorValue> {
        match indicator_type {
            IndicatorType::SMA(period) => self.calculate_sma(*period),
            IndicatorType::EMA(period) => self.calculate_ema(*period),
            IndicatorType::RSI(period) => self.calculate_rsi(*period),
            IndicatorType::BollingerBands { period, std_dev } => {
                self.calculate_bollinger_bands(*period, *std_dev)
            }
            IndicatorType::MACD { fast, slow, signal } => {
                self.calculate_macd(*fast, *slow, *signal)
            }
            IndicatorType::VWAP => self.calculate_vwap(),
            IndicatorType::ATR(period) => self.calculate_atr(*period),
            IndicatorType::Stochastic { k_period, d_period } => {
                self.calculate_stochastic(*k_period, *d_period)
            }
        }
    }
    
    /// 计算简单移动平均线
    fn calculate_sma(&self, period: u32) -> Result<IndicatorValue> {
        let period = period as usize;
        if self.price_history.len() < period {
            return Err(BacktestError::Data(format!(
                "Insufficient data for SMA calculation: need {}, have {}",
                period,
                self.price_history.len()
            )));
        }
        
        let sum: f64 = self.price_history
            .iter()
            .rev()
            .take(period)
            .map(|p| p.close)
            .sum();
        
        let sma = sum / period as f64;
        Ok(IndicatorValue::Single(sma))
    }
    
    /// 计算指数移动平均线
    fn calculate_ema(&self, period: u32) -> Result<IndicatorValue> {
        let period = period as usize;
        if self.price_history.len() < period {
            return Err(BacktestError::Data(format!(
                "Insufficient data for EMA calculation: need {}, have {}",
                period,
                self.price_history.len()
            )));
        }
        
        let multiplier = 2.0 / (period as f64 + 1.0);
        let mut ema = self.price_history[0].close;
        
        for price in self.price_history.iter().skip(1) {
            ema = (price.close * multiplier) + (ema * (1.0 - multiplier));
        }
        
        Ok(IndicatorValue::Single(ema))
    }
    
    /// 计算相对强弱指数
    fn calculate_rsi(&self, period: u32) -> Result<IndicatorValue> {
        let period = period as usize;
        if self.price_history.len() < period + 1 {
            return Err(BacktestError::Data(format!(
                "Insufficient data for RSI calculation: need {}, have {}",
                period + 1,
                self.price_history.len()
            )));
        }
        
        let mut gains = Vec::new();
        let mut losses = Vec::new();
        
        for i in 1..self.price_history.len() {
            let change = self.price_history[i].close - self.price_history[i - 1].close;
            if change > 0.0 {
                gains.push(change);
                losses.push(0.0);
            } else {
                gains.push(0.0);
                losses.push(-change);
            }
        }
        
        if gains.len() < period {
            return Err(BacktestError::Data("Insufficient price changes for RSI".to_string()));
        }
        
        let avg_gain: f64 = gains.iter().rev().take(period).sum::<f64>() / period as f64;
        let avg_loss: f64 = losses.iter().rev().take(period).sum::<f64>() / period as f64;
        
        if avg_loss == 0.0 {
            return Ok(IndicatorValue::Single(100.0));
        }
        
        let rs = avg_gain / avg_loss;
        let rsi = 100.0 - (100.0 / (1.0 + rs));
        
        Ok(IndicatorValue::Single(rsi))
    }
    
    /// 计算布林带
    fn calculate_bollinger_bands(&self, period: u32, std_dev: f64) -> Result<IndicatorValue> {
        let period = period as usize;
        if self.price_history.len() < period {
            return Err(BacktestError::Data(format!(
                "Insufficient data for Bollinger Bands calculation: need {}, have {}",
                period,
                self.price_history.len()
            )));
        }
        
        // 计算SMA
        let prices: Vec<f64> = self.price_history
            .iter()
            .rev()
            .take(period)
            .map(|p| p.close)
            .collect();
        
        let sma = prices.iter().sum::<f64>() / period as f64;
        
        // 计算标准差
        let variance = prices
            .iter()
            .map(|price| (price - sma).powi(2))
            .sum::<f64>() / period as f64;
        
        let std_deviation = variance.sqrt();
        
        let upper_band = sma + (std_dev * std_deviation);
        let lower_band = sma - (std_dev * std_deviation);
        
        Ok(IndicatorValue::Multiple(vec![upper_band, sma, lower_band]))
    }
    
    /// 计算MACD
    fn calculate_macd(&self, fast: u32, slow: u32, signal: u32) -> Result<IndicatorValue> {
        if self.price_history.len() < slow as usize {
            return Err(BacktestError::Data(format!(
                "Insufficient data for MACD calculation: need {}, have {}",
                slow,
                self.price_history.len()
            )));
        }
        
        // 计算快线和慢线EMA
        let fast_ema = self.calculate_ema(fast)?.as_single().unwrap();
        let slow_ema = self.calculate_ema(slow)?.as_single().unwrap();
        
        let macd_line = fast_ema - slow_ema;
        
        // 这里简化了信号线的计算，实际应该是MACD线的EMA
        let signal_line = macd_line * 0.9; // 占位实现
        let histogram = macd_line - signal_line;
        
        let mut result = HashMap::new();
        result.insert("macd".to_string(), macd_line);
        result.insert("signal".to_string(), signal_line);
        result.insert("histogram".to_string(), histogram);
        
        Ok(IndicatorValue::KeyValue(result))
    }
    
    /// 计算成交量加权平均价格
    fn calculate_vwap(&self) -> Result<IndicatorValue> {
        if self.price_history.is_empty() {
            return Err(BacktestError::Data("No data for VWAP calculation".to_string()));
        }
        
        let mut total_volume = 0.0;
        let mut total_pv = 0.0;
        
        for price in &self.price_history {
            let typical_price = price.typical_price();
            total_pv += typical_price * price.volume;
            total_volume += price.volume;
        }
        
        if total_volume == 0.0 {
            return Err(BacktestError::Data("Zero volume for VWAP calculation".to_string()));
        }
        
        let vwap = total_pv / total_volume;
        Ok(IndicatorValue::Single(vwap))
    }
    
    /// 计算平均真实波幅
    fn calculate_atr(&self, period: u32) -> Result<IndicatorValue> {
        let period = period as usize;
        if self.price_history.len() < period + 1 {
            return Err(BacktestError::Data(format!(
                "Insufficient data for ATR calculation: need {}, have {}",
                period + 1,
                self.price_history.len()
            )));
        }
        
        let mut true_ranges = Vec::new();
        
        for i in 1..self.price_history.len() {
            let current = &self.price_history[i];
            let prev_close = self.price_history[i - 1].close;
            let tr = current.true_range(Some(prev_close));
            true_ranges.push(tr);
        }
        
        if true_ranges.len() < period {
            return Err(BacktestError::Data("Insufficient true ranges for ATR".to_string()));
        }
        
        let atr = true_ranges.iter().rev().take(period).sum::<f64>() / period as f64;
        Ok(IndicatorValue::Single(atr))
    }
    
    /// 计算随机指标
    fn calculate_stochastic(&self, k_period: u32, d_period: u32) -> Result<IndicatorValue> {
        let k_period = k_period as usize;
        if self.price_history.len() < k_period {
            return Err(BacktestError::Data(format!(
                "Insufficient data for Stochastic calculation: need {}, have {}",
                k_period,
                self.price_history.len()
            )));
        }
        
        let recent_prices: Vec<&PriceData> = self.price_history
            .iter()
            .rev()
            .take(k_period)
            .collect();
        
        let highest_high = recent_prices.iter().map(|p| p.high).fold(f64::NEG_INFINITY, f64::max);
        let lowest_low = recent_prices.iter().map(|p| p.low).fold(f64::INFINITY, f64::min);
        let current_close = self.price_history.back().unwrap().close;
        
        let k_percent = if highest_high == lowest_low {
            50.0 // 避免除零
        } else {
            ((current_close - lowest_low) / (highest_high - lowest_low)) * 100.0
        };
        
        // 简化的%D计算（实际应该是%K的移动平均）
        let d_percent = k_percent * 0.9; // 占位实现
        
        let mut result = HashMap::new();
        result.insert("k".to_string(), k_percent);
        result.insert("d".to_string(), d_percent);
        
        Ok(IndicatorValue::KeyValue(result))
    }
    
    /// 获取历史数据长度
    pub fn history_length(&self) -> usize {
        self.price_history.len()
    }
    
    /// 清空历史数据
    pub fn clear_history(&mut self) {
        self.price_history.clear();
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use chrono::Utc;
    
    #[test]
    fn test_sma_calculation() {
        let mut calculator = IndicatorCalculator::new(100);
        
        // 添加测试数据
        for i in 1..=10 {
            let price = PriceData::new(
                Utc::now(),
                i as f64, i as f64, i as f64, i as f64, 100.0
            );
            calculator.add_price_data(price);
        }
        
        // 计算5期SMA
        let result = calculator.calculate(&IndicatorType::SMA(5)).unwrap();
        let sma = result.as_single().unwrap();
        
        // 最后5个数字的平均值: (6+7+8+9+10)/5 = 8.0
        assert_eq!(sma, 8.0);
    }
}
