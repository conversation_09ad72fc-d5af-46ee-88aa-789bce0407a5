exchange = "Binance"
start_time = "2025-07-07T11:23:20Z"
end_time = "2025-07-07T11:25:00Z"
websocket_port = 8082
http_port = 8083
log_level = "info"
performance_target_us = 500
data_source_type = "Tardis"

# HTTP服务器TLS配置
[http_tls]
# 启用TLS（HTTPS）
enabled = true

# 使用证书文件
[http_tls.cert_source]
type = "Files"
cert_path = "./certs/server.crt"
key_path = "./certs/server.key"

# WebSocket服务器TLS配置
[websocket_tls]
# 启用TLS（WSS）
enabled = true

# 使用证书文件
[websocket_tls.cert_source]
type = "Files"
cert_path = "./certs/server.crt"
key_path = "./certs/server.key"

[data_paths]
root = "./data"
quotes = "./data/quotes"
trades = "./data/trades"

[account]
account_type = "Futures"
initial_balance = 10000.0
max_leverage = 10.0
margin_mode = "Cross"
maker_fee_rate = 0.0002
taker_fee_rate = 0.0004
funding_rate = 0.0001
supported_symbols = ["BTCUSDT", "ETHUSDT"]
