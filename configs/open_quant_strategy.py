import json
import time

import traderv2 # type: ignore
import base_strategy

class Strategy(base_strategy.BaseStrategy):
    def __init__(self, cex_configs, dex_configs, config, trader: traderv2.TraderV2):
        self.trader = trader
        pass

    def name(self):
        return "Backtest"

    def start(self):
        pass

    def subscribes(self):
        subs = [
             {
                "account_id": 0,
                "sub": {
                    "SubscribeWs": [
                        {
                            "Bbo": ["BTC_USDT"]
                        }
                    ]
                }
            },
        ]
        return subs

    def on_bbo(self, exchange, bbo):
        self.trader.log(f"收到BBO数据: {bbo}", level="INFO", color="blue")

    def on_depth(self, exchange, depth):
        pass

    def on_order_submitted(self, account_id, order_id_result, order):
        pass

    def on_order_and_fill(self, account_id, order):
        pass

    def on_order(self, account_id, order):
        pass
