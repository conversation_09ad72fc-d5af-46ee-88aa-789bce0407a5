use crate::types::{MarketData, TimeBarrier};
use crate::{BacktestError, Result};
use tokio::sync::{broadcast, mpsc};
use tracing::{debug, error, info};

/// 数据处理器
/// 负责处理时间屏障和数据同步
pub struct DataProcessor {
    input_rx: mpsc::Receiver<MarketData>,
    output_tx: broadcast::Sender<MarketData>,
    current_barrier: Option<TimeBarrier>,
}

impl DataProcessor {
    /// 创建新的数据处理器
    pub fn new(
        input_rx: mpsc::Receiver<MarketData>,
        output_tx: broadcast::Sender<MarketData>,
    ) -> Self {
        Self {
            input_rx,
            output_tx,
            current_barrier: None,
        }
    }

    /// 开始处理数据
    pub async fn start_processing(&mut self) -> Result<()> {
        info!("Starting data processing");

        while let Some(market_data) = self.input_rx.recv().await {
            match self.process_market_data(market_data).await {
                Ok(()) => {}
                Err(e) => {
                    error!("Failed to process market data: {}", e);
                    continue;
                }
            }
        }

        info!("Data processing completed");
        Ok(())
    }

    /// 处理单个市场数据
    async fn process_market_data(&mut self, market_data: MarketData) -> Result<()> {
        // 检查时间屏障
        if self.should_process_data(&market_data) {
            // 更新时间屏障
            self.update_barrier_from_data(&market_data);

            if let Err(e) = self.output_tx.send(market_data) {
                error!("Failed to broadcast market data: {}", e);
                return Err(BacktestError::Communication(format!(
                    "Failed to broadcast data: {}",
                    e
                )));
            }

            debug!("Processed and broadcasted market data");
        } else {
            debug!("Data filtered by time barrier");
        }

        Ok(())
    }

    /// 判断是否应该处理数据（基于时间屏障）
    fn should_process_data(&self, market_data: &MarketData) -> bool {
        match &self.current_barrier {
            None => true, // 没有屏障，处理所有数据
            Some(barrier) => self.compare_with_barrier(market_data, barrier),
        }
    }

    /// 比较数据与时间屏障
    fn compare_with_barrier(&self, market_data: &MarketData, barrier: &TimeBarrier) -> bool {
        match (market_data, barrier) {
            // 基于时间戳的比较
            (MarketData::OrderBook(snapshot), TimeBarrier::Timestamp(barrier_time)) => {
                snapshot.timestamp >= *barrier_time
            }
            (MarketData::Trade(trade), TimeBarrier::Timestamp(barrier_time)) => {
                trade.timestamp.map_or(false, |ts| ts >= *barrier_time)
            }

            // 基于update_id的比较
            (MarketData::OrderBook(snapshot), TimeBarrier::UpdateId(barrier_id)) => {
                snapshot.update_id.map_or(false, |id| id >= *barrier_id)
            }
            (MarketData::Bbo(bbo), TimeBarrier::UpdateId(barrier_id)) => {
                bbo.update_id >= *barrier_id
            }

            // 其他情况默认处理
            _ => true,
        }
    }

    /// 从数据更新时间屏障
    fn update_barrier_from_data(&mut self, market_data: &MarketData) {
        match market_data {
            MarketData::OrderBook(snapshot) => {
                // 优先使用时间戳，其次使用update_id
                if let Some(timestamp) = Some(snapshot.timestamp) {
                    self.current_barrier = Some(TimeBarrier::Timestamp(timestamp));
                } else if let Some(update_id) = snapshot.update_id {
                    self.current_barrier = Some(TimeBarrier::UpdateId(update_id));
                }
            }
            MarketData::Bbo(bbo) => {
                self.current_barrier = Some(TimeBarrier::UpdateId(bbo.update_id));
            }
            MarketData::Trade(trade) => {
                if let Some(timestamp) = trade.timestamp {
                    self.current_barrier = Some(TimeBarrier::Timestamp(timestamp));
                }
            }
            MarketData::BookTicker(bookticker) => {
                // 使用事件时间作为时间屏障
                let event_datetime = bookticker.event_datetime();
                self.current_barrier = Some(TimeBarrier::Timestamp(event_datetime));
            }

            MarketData::TradeData(trade_data) => {
                // 使用时间戳作为时间屏障
                let timestamp_datetime = trade_data.timestamp_datetime();
                self.current_barrier = Some(TimeBarrier::Timestamp(timestamp_datetime));
            }
        }
    }

    /// 手动设置时间屏障
    pub fn set_time_barrier(&mut self, barrier: TimeBarrier) {
        self.current_barrier = Some(barrier);
    }

    /// 获取当前时间屏障
    pub fn get_current_barrier(&self) -> Option<&TimeBarrier> {
        self.current_barrier.as_ref()
    }

    /// 重置时间屏障
    pub fn reset_barrier(&mut self) {
        self.current_barrier = None;
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::types::{Bbo, Price};

    #[tokio::test]
    async fn test_data_processor() {
        let (input_tx, input_rx) = mpsc::channel(100);
        let (output_tx, _output_rx) = broadcast::channel(100);

        let mut processor = DataProcessor::new(input_rx, output_tx);

        // 测试BBO数据处理
        let bbo = Bbo {
            update_id: 12345,
            bid_price: Price::new(99.5),
            bid_quantity: 10.0,
            ask_price: Price::new(100.5),
            ask_quantity: 15.0,
        };

        let market_data = MarketData::Bbo(bbo);

        // 发送测试数据
        input_tx.send(market_data).await.unwrap();
        drop(input_tx);

        // 处理数据
        processor.start_processing().await.unwrap();
    }
}
