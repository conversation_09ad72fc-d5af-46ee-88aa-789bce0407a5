exchange = "Binance"
start_time = "2025-07-07T11:23:20Z"
end_time = "2025-07-07T11:25:00Z"
websocket_port = 8080
http_port = 8081
log_level = "info"
performance_target_us = 500
data_source_type = "Tardis"

[http_tls]
enabled = false

[websocket_tls]
enabled = false

[data_paths]
root = "./data"
quotes = "./data/quotes"
trades = "./data/trades"

[account]
account_type = "Futures"
initial_balance = 10000.0
max_leverage = 10.0
margin_mode = "Cross"
maker_fee_rate = 0.0002
taker_fee_rate = 0.0004
funding_rate = 0.0001
supported_symbols = ["BTCUSDT", "ETHUSDT"]
