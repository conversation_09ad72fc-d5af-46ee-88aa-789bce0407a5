# Simple test configuration
exchange = "Binance"
start_time = "2025-05-01T00:00:00Z"
end_time = "2025-05-01T01:00:00Z"
websocket_port = 8082
http_port = 8083
log_level = "info"
performance_target_us = 500

# Data source configuration
data_source_type = "Tardis"
tardis_data_type = "Trades"

# Data paths
[data_paths]
root = "./data"
quotes = "./data/quotes"
trades = "./data/trades"

# HTTP TLS configuration
[http_tls]
enabled = false

# WebSocket TLS configuration
[websocket_tls]
enabled = false

# Account configuration
[account]
account_type = "Futures"
initial_balance = 10000.0
max_leverage = 10.0
margin_mode = "Cross"
maker_fee_rate = 0.0002
taker_fee_rate = 0.0004
funding_rate = 0.0001
supported_symbols = ["BTCUSDT"]
